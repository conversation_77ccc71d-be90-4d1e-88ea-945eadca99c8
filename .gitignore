# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# Windows related
/windows/
/windows1/
# macOS related
# /macos/
# Linux related
/linux/
# Android related
# /android/
# iOS related
# /ios/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Specific files related to build_runner
generated_plugin_registrant.dart
*.g.dart

# fvm related
.fvm/
.fvmrc

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

.vscode
.augmentcode

# FVM Version Cache
.fvm/

# release related
scripts/releases/*.json
scripts/releases/keystore
.env
.env.*
*.p8
*tmp
*.zip
.fastlane
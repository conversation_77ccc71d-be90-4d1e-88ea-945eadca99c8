import 'dart:io';

import 'package:ddone/components/common/common_card.dart';
import 'package:ddone/components/common/round_shape_avatar.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/constants/value_constants.dart';
import 'package:ddone/cubit/chat_recent/chat_recent_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/delete_bookmark/delete_bookmark_cubit.dart';
import 'package:ddone/cubit/invitation/invitation_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:ddone/cubit/messaging/messages_cubit.dart';
import 'package:ddone/cubit/messaging/receive_message_cubit.dart';
import 'package:ddone/cubit/selected_contact/selected_contact_cubit.dart';
import 'package:ddone/cubit/update/chat_ui_cubit.dart';
import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/cubit/update/info_cubit.dart';
import 'package:ddone/cubit/version_management/version_management_cubit.dart';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/cubit/xmpp/connection/xmpp_connection_cubit.dart';
import 'package:ddone/environments/env.dart';
import 'package:ddone/cubit/auth/auth_cubit.dart';
import 'package:ddone/cubit/common/network/network_cubit.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/screens/settings.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/services/native/notification_native_service.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/pop_out_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:image/image.dart' as img;
import 'package:flutter/material.dart';
import 'package:file_selector_platform_interface/file_selector_platform_interface.dart';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:launch_at_startup/launch_at_startup.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:ddone/cubit/members_presence/members_presence_cubit.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:zxing2/qrcode.dart';

class AboutMe extends StatefulWidget {
  static const routeName = '/aboutMe';

  const AboutMe({super.key});
  // const AboutMe({Key? key, required this.contactId, required this.displayName, required this.uri}) : super(key: key);
  // final String contactId;
  // final String displayName;
  // final String uri;

  @override
  State<AboutMe> createState() => _AboutMeState();
}

class _AboutMeState extends State<AboutMe> with AutomaticKeepAliveClientMixin<AboutMe>, WidgetsBindingObserver {
  @override
  bool get wantKeepAlive => true;

  late AuthCubit _authCubit;
  late HomeCubit _homeCubit;
  late VoipCubit _voipCubit;
  late NetworkCubit _networkCubit;
  late ContactsCubit _contactsCubit;
  late SelectedContactCubit _selectedContactCubit;
  late InvitationCubit _invitationCubit;
  late MessagesCubit _messagesCubit;
  late MamListCubit _mamListCubit;
  late BookmarkListCubit _bookmarkListCubit;
  late PresencesCubit _presencesCubit;
  late ChatRecentCubit _chatRecentCubit;
  late LoginCubit _loginCubit;
  late ChatUiCubit _chatUiCubit;
  late GroupUiCubit _groupUiCubit;
  late DeleteBookmarkCubit _deleteBookmarkCubit;
  late ReceiveMessageCubit _receiveMessageCubit;
  late InfoCubit _infoCubit;
  late XmppConnectionCubit _xmppConnectionCubit;
  late VersionManagementCubit _versionManagementCubit;

  late NotificationNativeService _notifcationNativeService;

  late TextEditingController _controller;
  final String _barcodeResults = '';
  bool _isValid = false;
  bool _isMacOsNotificationPermissionEnabled = false;
  bool _isAutolaunchEnabled = false;

  GlobalKey<FormState> formKey = GlobalKey();

  TextEditingController proxyController = TextEditingController(text: '');
  TextEditingController domainController = TextEditingController(text: '');
  TextEditingController nameController = TextEditingController(text: '');
  TextEditingController secretController = TextEditingController(text: '');
  TextEditingController contactController = TextEditingController(text: '');
  TextEditingController wsController = TextEditingController(text: '');
  TextEditingController usernameController = TextEditingController(text: '');

  Widget getDefaultImage() {
    if (_controller.text.isEmpty || !_isValid) {
      return Image.asset('$imagePathPrefix/default.png');
    } else {
      return Image.file(File(_controller.text));
    }
  }

  // Map credentials = {
  //   'creds1': {
  //     'server': '*************',
  //     'name': '500',
  //     'domain': '*************',
  //     'secret': 'ZB3p7%v13Wp*TTy5KEyx',
  //     'username': 'sip:500@*************',
  //   },
  //   'creds2': {
  //     'server': '*************',
  //     'name': '505',
  //     'domain': '*************',
  //     'secret': '6H6.8wzvHgn9baNwF%mP',
  //     'username': 'sip:505@*************',
  //   },
  //   'creds3': {
  //     'server': 'trp.uc.dotdashtech.com',
  //     'name': '5050',
  //     'domain': 'trp.uc.dotdashtech.com',
  //     'secret': 'H9iPFBGj2MI!sWL^k.xH',
  //     'username': 'sip:<EMAIL>',
  //   },
  // };

  Future<void> openRegisterDialog() async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return BlocBuilder<ThemeCubit, ThemeState>(
              builder: (context, themeState) {
                final colorTheme = themeState.colorTheme;
                final textTheme = themeState.themeData.textTheme;

                return BlocListener<AuthCubit, AuthState>(
                  listener: (context, authState) {
                    if (authState is AuthSuccess) {
                      pop();

                      EasyLoadingService().dismissLoading();

                      EasyLoadingService().showSuccessWithText('Successfully registered!');
                    } else if (authState is AuthRegistering) {
                      EasyLoadingService().showLoadingWithText('Registering...');
                    } else if (authState is AuthFail) {
                      //All error message being handle at auth_cubit
                    }
                  },
                  child: AlertDialog(
                    backgroundColor: Colors.grey[100],
                    insetPadding: EdgeInsets.zero,
                    title: Text(
                      'Register QR',
                      style: context
                          .responsiveSize<TextStyle>(
                            moileSize: textTheme.titleLarge!,
                            tabletSize: textTheme.titleLarge!,
                            desktopSize: textTheme.headlineLarge!,
                            largeScreenSize: textTheme.headlineLarge!,
                          )
                          .copyWith(color: colorTheme.backgroundColor),
                    ),
                    content: SizedBox(
                      width: context.deviceWidth(0.6),
                      height: context.deviceHeight(0.6),
                      child: Form(
                        key: formKey,
                        child: Column(
                          children: [
                            SizedBox(height: context.deviceHeight(0.01)),
                            TextField(
                              style: textTheme.labelLarge!.copyWith(color: colorTheme.roundShapeInkWellColor),
                              enabled: false,
                              controller: _controller,
                              decoration: InputDecoration(
                                labelText: 'Image path',
                                labelStyle: context
                                    .responsiveSize<TextStyle>(
                                      moileSize: textTheme.titleSmall!,
                                      tabletSize: textTheme.titleSmall!,
                                      desktopSize: textTheme.titleLarge!,
                                      largeScreenSize: textTheme.titleLarge!,
                                    )
                                    .copyWith(color: colorTheme.roundShapeInkWellColor),
                                errorStyle: context
                                    .responsiveSize<TextStyle>(
                                      moileSize: textTheme.titleSmall!,
                                      tabletSize: textTheme.titleSmall!,
                                      desktopSize: textTheme.titleLarge!,
                                      largeScreenSize: textTheme.titleLarge!,
                                    )
                                    .copyWith(color: colorTheme.errorColor),
                                errorText: _isValid ? null : 'File not exists',
                              ),
                            ),
                            SizedBox(height: context.deviceHeight(0.01)),
                            Expanded(
                              child: SingleChildScrollView(
                                child: Column(
                                  children: [
                                    getDefaultImage(),
                                    Text(
                                      _barcodeResults,
                                      style: textTheme.bodyMedium!.copyWith(color: colorTheme.roundShapeInkWellColor),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            SizedBox(height: context.deviceHeight(0.01)),
                            SizedBox(
                              width: context.responsiveSize(
                                moileSize: widthXXXLarge,
                                tabletSize: widthXXXLarge,
                                desktopSize: context.deviceWidth(0.23),
                                largeScreenSize: context.deviceWidth(0.23),
                              ),
                              child: ElevatedButton(
                                onPressed: () async {
                                  if (isMobile) {
                                    showCameraGalleryBottomSheetMultiple(
                                      context,
                                      showQr: true,
                                      showCamera: false,
                                      allowedMultipick: false,
                                      qrDataCallBack: (value) async {
                                        if (value == null) {
                                          return;
                                        }

                                        debugPrint('qrDataCallBack: $value');

                                        await _authCubit.logInWithUrl(
                                          apiUrl: value,
                                          voipCubit: _voipCubit,
                                          homeCubit: _homeCubit,
                                          authCubit: _authCubit,
                                          contactsCubit: _contactsCubit,
                                          loginCubit: _loginCubit,
                                          invitationCubit: _invitationCubit,
                                          messagesCubit: _messagesCubit,
                                          mamListCubit: _mamListCubit,
                                          bookmarkListCubit: _bookmarkListCubit,
                                          presencesCubit: _presencesCubit,
                                          chatRecentCubit: _chatRecentCubit,
                                          chatUiCubit: _chatUiCubit,
                                          groupUiCubit: _groupUiCubit,
                                          deleteBookmarkCubit: _deleteBookmarkCubit,
                                          receiveMessageCubit: _receiveMessageCubit,
                                          xmppConnectionCubit: _xmppConnectionCubit,
                                          versionManagementCubit: _versionManagementCubit,
                                          networkCubit: _networkCubit,
                                        );
                                      },
                                      photoListCallBack: (listValue) {
                                        if (listValue != null && listValue.isNotEmpty) {
                                          String apiUrl = decodeQR(File(listValue[0]));

                                          _authCubit.logInWithUrl(
                                            apiUrl: apiUrl,
                                            voipCubit: _voipCubit,
                                            homeCubit: _homeCubit,
                                            authCubit: _authCubit,
                                            contactsCubit: _contactsCubit,
                                            loginCubit: _loginCubit,
                                            invitationCubit: _invitationCubit,
                                            messagesCubit: _messagesCubit,
                                            mamListCubit: _mamListCubit,
                                            bookmarkListCubit: _bookmarkListCubit,
                                            presencesCubit: _presencesCubit,
                                            chatRecentCubit: _chatRecentCubit,
                                            chatUiCubit: _chatUiCubit,
                                            groupUiCubit: _groupUiCubit,
                                            deleteBookmarkCubit: _deleteBookmarkCubit,
                                            receiveMessageCubit: _receiveMessageCubit,
                                            xmppConnectionCubit: _xmppConnectionCubit,
                                            versionManagementCubit: _versionManagementCubit,
                                            networkCubit: _networkCubit,
                                          );
                                        }
                                      },
                                    );
                                  } else {
                                    try {
                                      const typeGroup = XTypeGroup(
                                        label: 'images',
                                        extensions: ['jpg', 'png'],
                                      );

                                      final files = await FileSelectorPlatform.instance
                                          .openFiles(acceptedTypeGroups: [typeGroup]);

                                      final file = files[0];

                                      String apiUrl = decodeQR(File(file.path));

                                      // List<BarcodeResult> results =
                                      //     await _barcodeReader.decodeFile(file.path);

                                      _isValid = true;

                                      setState(() {
                                        _controller.text = file.path;

                                        getDefaultImage();
                                        // _barcodeResults = getBarcodeResults(results);
                                      });

                                      // _getDataNRegister(apiUrl, _janusCubit);
                                      await _authCubit.logInWithUrl(
                                        apiUrl: apiUrl,
                                        voipCubit: _voipCubit,
                                        homeCubit: _homeCubit,
                                        authCubit: _authCubit,
                                        contactsCubit: _contactsCubit,
                                        loginCubit: _loginCubit,
                                        invitationCubit: _invitationCubit,
                                        messagesCubit: _messagesCubit,
                                        mamListCubit: _mamListCubit,
                                        bookmarkListCubit: _bookmarkListCubit,
                                        presencesCubit: _presencesCubit,
                                        chatRecentCubit: _chatRecentCubit,
                                        chatUiCubit: _chatUiCubit,
                                        groupUiCubit: _groupUiCubit,
                                        deleteBookmarkCubit: _deleteBookmarkCubit,
                                        receiveMessageCubit: _receiveMessageCubit,
                                        xmppConnectionCubit: _xmppConnectionCubit,
                                        versionManagementCubit: _versionManagementCubit,
                                        networkCubit: _networkCubit,
                                      );

                                      _controller.clear();
                                    } catch (err) {
                                      EasyLoadingService().showErrorWithText('No QR code selected.');
                                    }
                                  }
                                },
                                style:
                                    ButtonStyle(backgroundColor: WidgetStateProperty.all(colorTheme.backgroundColor)),
                                child: const Text(
                                  'Select QR Image',
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                            SizedBox(height: context.deviceHeight(0.01)),
                            SizedBox(
                              width: context.responsiveSize(
                                moileSize: widthXXXLarge,
                                tabletSize: widthXXXLarge,
                                desktopSize: context.deviceWidth(0.23),
                                largeScreenSize: context.deviceWidth(0.23),
                              ),
                              child: ElevatedButton(
                                style:
                                    ButtonStyle(backgroundColor: WidgetStateProperty.all(colorTheme.backgroundColor)),
                                onPressed: () {
                                  pop();
                                },
                                child: const Text(
                                  'Cancel',
                                  style: TextStyle(color: Colors.red),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  Future<void> openManualRegisterDialog() async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return BlocBuilder<ThemeCubit, ThemeState>(
              builder: (context, themeState) {
                final colorTheme = themeState.colorTheme;
                final textTheme = themeState.themeData.textTheme;

                return BlocListener<AuthCubit, AuthState>(
                  listener: (context, authState) {
                    if (authState is AuthSuccess) {
                      pop();

                      EasyLoadingService().dismissLoading();

                      EasyLoadingService().showSuccessWithText('Successfully registered!');

                      _contactsCubit.getContactList();
                    } else if (authState is AuthRegistering) {
                      EasyLoadingService().showLoadingWithText('Registering...');
                    } else if (authState is AuthFail) {
                      //All error message being handle at auth_cubit
                    }
                  },
                  child: AlertDialog(
                    backgroundColor: Colors.grey[100],
                    insetPadding: EdgeInsets.zero,
                    title: Text(
                      'Manual Register',
                      style: textTheme.headlineLarge!.copyWith(color: colorTheme.backgroundColor),
                    ),
                    content: SizedBox(
                      width: context.deviceWidth(0.65),
                      height: context.deviceHeight(0.57),
                      child: Form(
                        key: formKey,
                        child: Column(
                          children: [
                            Flexible(
                                child: TextFormField(
                              style: textTheme.displaySmall!.copyWith(color: colorTheme.backgroundColor),
                              decoration: InputDecoration(
                                labelText: 'Server',
                                hintText: 'Server ip',
                                labelStyle: textTheme.displaySmall!.copyWith(color: colorTheme.backgroundColor),
                              ),
                              controller: proxyController,
                              validator: (val) {
                                if (val == '') {
                                  return 'uri can\'t be empty! ';
                                }
                                return null;
                              },
                            )),
                            Flexible(
                                child: TextFormField(
                              style: textTheme.displaySmall!.copyWith(color: colorTheme.backgroundColor),
                              decoration: InputDecoration(
                                labelText: 'Username',
                                hintText: '001',
                                labelStyle: textTheme.displaySmall!.copyWith(color: colorTheme.backgroundColor),
                              ),
                              controller: nameController,
                              validator: (val) {
                                if (val == '') {
                                  return 'uri can\'t be empty! ';
                                }
                                return null;
                              },
                            )),
                            Flexible(
                              child: TextFormField(
                                style: textTheme.displaySmall!.copyWith(color: colorTheme.backgroundColor),
                                decoration: InputDecoration(
                                  labelText: 'Domain',
                                  hintText: 'hello.com',
                                  labelStyle: textTheme.displaySmall!.copyWith(color: colorTheme.backgroundColor),
                                ),
                                controller: domainController,
                                validator: (val) {
                                  if (val == '') {
                                    return 'uri can\'t be empty! ';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            Flexible(
                              child: TextFormField(
                                style: textTheme.displaySmall!.copyWith(color: colorTheme.backgroundColor),
                                decoration: InputDecoration(
                                  labelText: 'Password',
                                  labelStyle: textTheme.displaySmall!.copyWith(color: colorTheme.backgroundColor),
                                ),
                                obscureText: true,
                                controller: secretController,
                                validator: (val) {
                                  if (val == '') {
                                    return 'uri can\'t be empty! ';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            Flexible(
                              child: TextFormField(
                                style: textTheme.displaySmall!.copyWith(color: colorTheme.backgroundColor),
                                decoration: InputDecoration(
                                  labelText: 'Contact Url',
                                  hintText: 'https://contact.com?domain=hello.com',
                                  labelStyle: textTheme.displaySmall!.copyWith(color: colorTheme.backgroundColor),
                                ),
                                controller: contactController,
                                validator: (val) {
                                  if (val == '') {
                                    return 'uri can\'t be empty! ';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            Flexible(
                              child: TextFormField(
                                style: textTheme.displaySmall!.copyWith(color: colorTheme.backgroundColor),
                                decoration: InputDecoration(
                                  labelText: 'Websocket',
                                  hintText: 'wss://wsip:port/ws',
                                  labelStyle: textTheme.displaySmall!.copyWith(color: colorTheme.backgroundColor),
                                ),
                                controller: wsController,
                                validator: (val) {
                                  if (val == '') {
                                    return 'uri can\'t be empty! ';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            Padding(padding: EdgeInsets.all(context.deviceWidth(0.01))),
                            BlocBuilder<VoipCubit, VoipState>(
                              builder: (context, voipState) {
                                return ElevatedButton(
                                  onPressed: () async {
                                    // sipUser = 'sip:' + nameController.text + '@' + domainController.text;

                                    if (voipState is VoipSipRegistered) {
                                      EasyLoading.showError('Account already registered');
                                    } else {
                                      // bool successReg = await rtc.registerUser(formKey, usernameController.text, proxyController.text, secretController.text);
                                      // _manualRegister(
                                      //   _janusCubit,
                                      //   nameController.text,
                                      //   domainController.text,
                                      //   proxyController.text,
                                      //   secretController.text,
                                      // );
                                      final contactUri = Uri.parse(contactController.text);
                                      _authCubit.manualLogIn(
                                        homeCubit: _homeCubit,
                                        voipCubit: _voipCubit,
                                        authCubit: _authCubit,
                                        contactsCubit: _contactsCubit,
                                        loginCubit: _loginCubit,
                                        invitationCubit: _invitationCubit,
                                        messagesCubit: _messagesCubit,
                                        mamListCubit: _mamListCubit,
                                        bookmarkListCubit: _bookmarkListCubit,
                                        presencesCubit: _presencesCubit,
                                        chatRecentCubit: _chatRecentCubit,
                                        groupUiCubit: _groupUiCubit,
                                        deleteBookmarkCubit: _deleteBookmarkCubit,
                                        chatUiCubit: _chatUiCubit,
                                        sipNumber: nameController.text,
                                        sipDomain: domainController.text,
                                        sipProxy: proxyController.text,
                                        sipSecret: secretController.text,
                                        sipContactUrlBase: contactUri.origin + contactUri.path,
                                        sipContactUrlParams: contactUri.query,
                                        sipWsUrl: wsController.text,
                                        pnUrl: '',
                                        receiveMessageCubit: _receiveMessageCubit,
                                        xmppConnectionCubit: _xmppConnectionCubit,
                                        versionManagementCubit: _versionManagementCubit,
                                      );

                                      // Login.userLogin(context, formKey, proxyController.text, proxyController.text, nameController.text, secretController.text);
                                      // Home.sipRegistered.addListener(() {
                                      //   // print("SIP DATA: ");
                                      //   // print(sip?.data?.first);
                                      //   if (Home.sipRegistered.value) {
                                      //     setState(() {
                                      //       Home.sipRegMsg = usernameController.text;
                                      //       Home.myServerIP = proxyController.text;

                                      //       sipName = nameController.text;
                                      //     });
                                      //     print("REGISTERED:" + usernameController.text);
                                      //     EasyLoading.showSuccess("Successfully registered!");
                                      //     pop();
                                      //   } else {
                                      //     EasyLoading.showError("Failed to register!");
                                      //   }
                                      // });
                                      // if (successReg){
                                      //   setState(() {
                                      //     sipRegMsg = usernameController.text;
                                      //   });
                                      //   print("REGISTERED:" + usernameController.text);
                                      //   EasyLoading.showSuccess("Successfully registered!");
                                      //   pop();
                                      // } else {
                                      //   EasyLoading.showError("Failed to register!");
                                      // }
                                    }
                                  },
                                  child: Text(
                                    'Proceed',
                                    style: textTheme.labelLarge!.copyWith(color: colorTheme.backgroundColor),
                                  ),
                                );
                              },
                            ),
                            SizedBox(height: context.deviceHeight(0.02)),
                            // ElevatedButton(
                            //   style: ButtonStyle(
                            //       backgroundColor:
                            //           MaterialStateProperty.all(Colors.red)),
                            //   onPressed: () {
                            //     pop();
                            //   },
                            //   child: Text("Cancel"),
                            // ),

                            // SizedBox(height: 20),
                            // SizedBox(
                            //   width: 200,
                            //   child: MaterialButton(
                            //   child: Text('Select QR Image'),
                            //   textColor: ddOrange,
                            //   color: greyBackground,
                            //   onPressed: () async {
                            //     final typeGroup = XTypeGroup(
                            //       label: 'images',
                            //       extensions: ['jpg', 'png'],
                            //     );
                            //     try {
                            //       final files = await FileSelectorPlatform.instance
                            //           .openFiles(acceptedTypeGroups: [typeGroup]);
                            //       final file = files[0];

                            //       String result = decodeQR(file.path);
                            //       if (result == "error"){
                            //         EasyLoading.showError("Image does not contain QR code");
                            //         return;
                            //       }
                            //       // List<BarcodeResult> results =
                            //       //     await _barcodeReader.decodeFile(file.path);

                            //       _isValid = true;
                            //       setState(() {
                            //         _controller.text = file.path;
                            //         getDefaultImage();
                            //         // _barcodeResults = getBarcodeResults(results);
                            //       });
                            //       _getDataNRegister(result);
                            //     } catch (err) {
                            //       print('Error: $err');
                            //     }
                            //   })),
                            // SizedBox(height: 20),
                            SizedBox(
                              width: context.deviceWidth(0.23),
                              child: ElevatedButton(
                                style:
                                    ButtonStyle(backgroundColor: WidgetStateProperty.all(colorTheme.backgroundColor)),
                                onPressed: pop,
                                child: const Text(
                                  'Cancel',
                                  style: TextStyle(color: Colors.red),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  String decodeQR(File file) {
    try {
      var image = img.decodeImage(file.readAsBytesSync())!;

      LuminanceSource source = RGBLuminanceSource(
          image.width,
          image.height,
          image
              .convert(format: img.Format.uint8, numChannels: 4)
              .getBytes(order: img.ChannelOrder.abgr)
              .buffer
              .asInt32List());
      var bitmap = BinaryBitmap(GlobalHistogramBinarizer(source));

      var reader = QRCodeReader();
      var result = reader.decode(bitmap);

      return (result.text);
    } catch (e) {
      debugPrint('decodeQR error: $e');

      return 'error';
    }
  }

  void callLogout() {
    _selectedContactCubit.removeSelectedContacInfo();

    _authCubit.logout(
      homeCubit: _homeCubit,
      voipCubit: _voipCubit,
      contactsCubit: _contactsCubit,
      loginCubit: _loginCubit,
      mamListCubit: _mamListCubit,
      bookmarkListCubit: _bookmarkListCubit,
      infoCubit: _infoCubit,
      xmppConnectionCubit: _xmppConnectionCubit,
    );
  }

  Future<void> launchUserGuideUrl() async {
    final Uri url = Uri.parse(env!.userGuideUrl);
    if (await canLaunchUrl(url)) {
      bool launched = await launchUrl(url, mode: LaunchMode.externalApplication);
      if (!launched) {
        log.d('Could not launch $url in external app');
      }
    } else {
      log.e('Cannot launch $url');
    }
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);

    _authCubit = BlocProvider.of<AuthCubit>(context);
    _homeCubit = BlocProvider.of<HomeCubit>(context);
    _voipCubit = BlocProvider.of<VoipCubit>(context);
    _networkCubit = BlocProvider.of<NetworkCubit>(context);
    _contactsCubit = BlocProvider.of<ContactsCubit>(context);
    _selectedContactCubit = BlocProvider.of<SelectedContactCubit>(context);
    _invitationCubit = BlocProvider.of<InvitationCubit>(context);
    _messagesCubit = BlocProvider.of<MessagesCubit>(context);
    _mamListCubit = BlocProvider.of<MamListCubit>(context);
    _bookmarkListCubit = BlocProvider.of<BookmarkListCubit>(context);
    _presencesCubit = BlocProvider.of<PresencesCubit>(context);
    _chatRecentCubit = BlocProvider.of<ChatRecentCubit>(context);
    _loginCubit = BlocProvider.of<LoginCubit>(context);
    _chatUiCubit = BlocProvider.of<ChatUiCubit>(context);
    _groupUiCubit = BlocProvider.of<GroupUiCubit>(context);
    _deleteBookmarkCubit = BlocProvider.of<DeleteBookmarkCubit>(context);
    _receiveMessageCubit = BlocProvider.of<ReceiveMessageCubit>(context);
    _infoCubit = BlocProvider.of<InfoCubit>(context);
    _xmppConnectionCubit = BlocProvider.of<XmppConnectionCubit>(context);
    _versionManagementCubit = BlocProvider.of<VersionManagementCubit>(context);

    _notifcationNativeService = sl.get<NotificationNativeService>();

    _controller = TextEditingController();

    _checkNotificationPermissionMacOS();

    _checkAutoLaunch();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    _controller.dispose();

    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      _checkNotificationPermissionMacOS();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;

        return Scaffold(
          appBar: AppBar(
            backgroundColor: colorTheme.backgroundColor,
            title: const Text('Account'),
            centerTitle: true,
            actions: <Widget>[
              BlocBuilder<AuthCubit, AuthState>(
                builder: (context, authState) {
                  return Padding(
                    padding: const EdgeInsets.fromLTRB(0, 0, 15, 0),
                    child: PopupMenuButton<String>(
                      splashRadius: context.deviceHeight(0.035),
                      color: colorTheme.onPrimaryColor,
                      onSelected: (String value) {
                        switch (value) {
                          case 'qr_code':
                            openRegisterDialog();

                            break;
                          case 'sipManual':
                            openManualRegisterDialog();

                            break;
                          case 'logout':
                            callLogout();

                            break;
                          case 'settings':
                            log.i('Pressed settings button');
                            pushNamed(SettingsScreen.routeName);
                            break;
                          case 'user_guide':
                            launchUserGuideUrl();

                            break;
                          case 'notification':
                            final notificationNativeService = sl.get<NotificationNativeService>();

                            notificationNativeService.openNotificationCenterMacOS();

                            break;
                          case 'autoLaunch':
                            if (_isAutolaunchEnabled) {
                              launchAtStartup.disable();

                              setState(() {
                                _isAutolaunchEnabled = false;
                              });
                            } else {
                              launchAtStartup.enable();

                              setState(() {
                                _isAutolaunchEnabled = true;
                              });
                            }

                            break;
                          default:
                            break;
                        }
                      },
                      icon: Icon(Icons.settings, color: colorTheme.primaryColor),
                      itemBuilder: (BuildContext context) {
                        final itemWidth = context.responsiveSize(
                          moileSize: widthXXXLarge,
                          tabletSize: width5XLarge,
                          desktopSize: context.deviceWidth(0.15),
                          largeScreenSize: context.deviceWidth(0.15),
                        );

                        return <PopupMenuEntry<String>>[
                          if (isDesktop)
                            PopupMenuItem(
                              value: 'autoLaunch',
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: <Widget>[
                                  const Padding(
                                    padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                    child: Icon(
                                      Icons.start_outlined,
                                      color: Colors.black38,
                                    ),
                                  ),
                                  SizedBox(
                                    width: itemWidth,
                                    child: Text('Auto launch: ${_isAutolaunchEnabled ? 'On' : 'Off'}',
                                        style: context
                                            .responsiveSize<TextStyle>(
                                              moileSize: textTheme.bodySmall!,
                                              tabletSize: textTheme.bodySmall!,
                                              desktopSize: textTheme.bodyLarge!,
                                              largeScreenSize: textTheme.bodyLarge!,
                                            )
                                            .copyWith(color: colorTheme.roundShapeInkWellColor)),
                                  ),
                                ],
                              ),
                            ),
                          if (isMacOS && authState is AuthSuccess)
                            PopupMenuItem(
                              value: 'notification',
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: <Widget>[
                                  const Padding(
                                    padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                    child: Icon(
                                      Icons.notifications_none,
                                      color: Colors.black38,
                                    ),
                                  ),
                                  SizedBox(
                                    width: itemWidth,
                                    child: Text('Notification: ${_isMacOsNotificationPermissionEnabled ? 'On' : 'Off'}',
                                        style: context
                                            .responsiveSize<TextStyle>(
                                              moileSize: textTheme.bodySmall!,
                                              tabletSize: textTheme.bodySmall!,
                                              desktopSize: textTheme.bodyLarge!,
                                              largeScreenSize: textTheme.bodyLarge!,
                                            )
                                            .copyWith(color: colorTheme.roundShapeInkWellColor)),
                                  ),
                                ],
                              ),
                            ),
                          if (authState is! AuthSuccess)
                            PopupMenuItem(
                              value: 'qr_code',
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: <Widget>[
                                  const Padding(
                                    padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                    child: Icon(
                                      Icons.sip,
                                      color: Colors.black38,
                                    ),
                                  ),
                                  SizedBox(
                                    width: itemWidth,
                                    child: Text('Register with QR',
                                        style: context
                                            .responsiveSize<TextStyle>(
                                              moileSize: textTheme.bodySmall!,
                                              tabletSize: textTheme.bodySmall!,
                                              desktopSize: textTheme.bodyLarge!,
                                              largeScreenSize: textTheme.bodyLarge!,
                                            )
                                            .copyWith(color: colorTheme.roundShapeInkWellColor)),
                                  ),
                                ],
                              ),
                            ),
                          if (authState is! AuthSuccess && env!.flavor != BuildFlavor.production)
                            PopupMenuItem(
                              value: 'sipManual',
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: <Widget>[
                                  const Padding(
                                    padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                    child: Icon(
                                      Icons.sip,
                                      color: Colors.black38,
                                    ),
                                  ),
                                  SizedBox(
                                    width: itemWidth,
                                    child: Text('Manual Register',
                                        style: context
                                            .responsiveSize<TextStyle>(
                                              moileSize: textTheme.bodySmall!,
                                              tabletSize: textTheme.bodySmall!,
                                              desktopSize: textTheme.bodyLarge!,
                                              largeScreenSize: textTheme.bodyLarge!,
                                            )
                                            .copyWith(color: colorTheme.roundShapeInkWellColor)),
                                  ),
                                ],
                              ),
                            ),
                          if (authState is AuthSuccess)
                            PopupMenuItem(
                              value: 'logout',
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: <Widget>[
                                  const Padding(
                                    padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                    child: Icon(
                                      Icons.logout,
                                      color: Colors.black38,
                                    ),
                                  ),
                                  SizedBox(
                                    width: itemWidth,
                                    child: Text('Log out',
                                        style: context
                                            .responsiveSize<TextStyle>(
                                              moileSize: textTheme.bodySmall!,
                                              tabletSize: textTheme.bodySmall!,
                                              desktopSize: textTheme.bodyLarge!,
                                              largeScreenSize: textTheme.bodyLarge!,
                                            )
                                            .copyWith(color: colorTheme.roundShapeInkWellColor)),
                                  ),
                                ],
                              ),
                            ),
                          if (authState is AuthSuccess)
                            PopupMenuItem(
                              value: 'settings',
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: <Widget>[
                                  const Padding(
                                    padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                    child: Icon(
                                      Icons.construction,
                                      color: Colors.black38,
                                    ),
                                  ),
                                  SizedBox(
                                    width: itemWidth,
                                    child: Text('Settings',
                                        style: context
                                            .responsiveSize<TextStyle>(
                                              moileSize: textTheme.bodySmall!,
                                              tabletSize: textTheme.bodySmall!,
                                              desktopSize: textTheme.bodyLarge!,
                                              largeScreenSize: textTheme.bodyLarge!,
                                            )
                                            .copyWith(color: colorTheme.roundShapeInkWellColor)),
                                  ),
                                ],
                              ),
                            ),
                          PopupMenuItem(
                            value: 'user_guide',
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: <Widget>[
                                const Padding(
                                  padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                  child: Icon(
                                    Icons.help_outline,
                                    color: Colors.black38,
                                  ),
                                ),
                                SizedBox(
                                  width: itemWidth,
                                  child: Text('Help',
                                      style: context
                                          .responsiveSize<TextStyle>(
                                            moileSize: textTheme.bodySmall!,
                                            tabletSize: textTheme.bodySmall!,
                                            desktopSize: textTheme.bodyLarge!,
                                            largeScreenSize: textTheme.bodyLarge!,
                                          )
                                          .copyWith(color: colorTheme.roundShapeInkWellColor)),
                                ),
                              ],
                            ),
                          ),
                        ];
                      },
                    ),
                  );
                },
              )
            ],
          ),
          body: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: maxWidthForDesktop),
              child: Container(
                // height: height * 0.85,
                color: colorTheme.backgroundColor,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      const RoundShapeAvatar(),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(0, 0, 0, 10),
                        child: Center(
                          child: Text(
                            '${env!.appName} v.${env!.appVersion}',
                            style: textTheme.displayMedium!.copyWith(color: colorTheme.primaryColor),
                          ),
                        ),
                      ),
                      BlocBuilder<NetworkCubit, NetworkState>(
                        builder: (context, networkState) {
                          return BlocBuilder<VoipCubit, VoipState>(
                            builder: (context, voipState) {
                              String connectionStatus = 'Offline';
                              IconData connectionIcon = Icons.wifi_off;
                              if (networkState is! NetworkDisconnected) {
                                if (voipState is VoipSipRegistered) {
                                  connectionStatus = 'Online';
                                  connectionIcon = Icons.network_wifi;
                                } else if (voipState is VoipSipRegistering || voipState is VoipSipHangingUp) {
                                  connectionStatus = 'Connecting';
                                  connectionIcon = Icons.network_check;
                                }
                              }
                              return Padding(
                                padding: EdgeInsets.fromLTRB(0, 0, 0, context.deviceHeight(0.02)),
                                child: Center(
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        connectionStatus,
                                        style: textTheme.displayMedium!.copyWith(color: colorTheme.onPrimaryColor),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(left: 8.0),
                                        child: Icon(connectionIcon),
                                      )
                                    ],
                                  ),
                                ),
                              );
                            },
                          );
                        },
                      ),
                      BlocBuilder<AuthCubit, AuthState>(
                        builder: (context, authState) {
                          return CommonCard(
                            title: 'Domain',
                            desc: authState.displaySipDomain,
                          );
                        },
                      ),
                      BlocBuilder<AuthCubit, AuthState>(
                        builder: (context, authState) {
                          return CommonCard(
                            title: 'Phone Number',
                            desc: authState.displaySipNumber,
                          );
                        },
                      ),
                      BlocBuilder<AuthCubit, AuthState>(
                        builder: (context, authState) {
                          return CommonCard(
                            title: 'Username',
                            desc: authState.displaySipName,
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _checkNotificationPermissionMacOS() async {
    _isMacOsNotificationPermissionEnabled = await _notifcationNativeService.checkNotificationPermissionMacOS();
  }

  void _checkAutoLaunch() async {
    if (isDesktop) {
      _isAutolaunchEnabled = await launchAtStartup.isEnabled();
    }
  }
}

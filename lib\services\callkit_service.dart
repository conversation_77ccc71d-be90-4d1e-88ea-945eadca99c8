import 'package:ddone/constants/callkit_constants.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_callkit_incoming/entities/android_params.dart';
import 'package:flutter_callkit_incoming/entities/call_event.dart';
import 'package:flutter_callkit_incoming/entities/call_kit_params.dart';
import 'package:flutter_callkit_incoming/entities/ios_params.dart';
import 'package:flutter_callkit_incoming/entities/notification_params.dart';
import 'package:flutter_callkit_incoming/flutter_callkit_incoming.dart';
import 'package:uuid/uuid.dart';

enum CallDirection {
  outgoing,
  incoming;
}

class CallkitService with PrefsAware {
  String? _uuid;
  CallDirection? _callDirection;
  bool acceptedCall = false;

  CallkitService();

  bool get isRinging => prefs.getBool(CacheKeys.callkitRinging) ?? false;
  String? get uuid => _uuid;

  Future<bool> _isAndroid14OrNewer() async {
    if (isAndroid) {
      final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      final AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      // Android 14 is API Level 34
      return androidInfo.version.sdkInt >= 34;
    }
    return false; // Not an Android device
  }

  /// Request Full Intent Permission permissions (Android 14+)
  Future<bool> requestFullIntentPermission() async {
    if (await _isAndroid14OrNewer()) {
      final canUseFullScreenIntent = await FlutterCallkitIncoming.canUseFullScreenIntent();
      log.t('callkitService - canUseFullScreenIntent: $canUseFullScreenIntent');
      if (canUseFullScreenIntent) {
        final requestFullIntentPermission = await FlutterCallkitIncoming.requestFullIntentPermission();
        log.t('callkitService - requestFullIntentPermission: $requestFullIntentPermission');
        return requestFullIntentPermission;
      }
      return false;
    }
    return true;
  }

  Future<void> init({
    Function()? incomingCallCallback,
    Function()? outgoingCallCallback,
    Function(CallDirection?)? acceptedCallCallback,
    Function(CallDirection?)? declinedCallCallback,
    Function()? endedCallCallback,
    Function()? missedCallCallback,
    Function()? callBackCallback,
    Function()? iosToggleHoldCallCallback,
    Function(bool)? iosToggleMuteCallCallback,
    Function(String)? iosToggleDmtfCallback,
    Function()? iosToggleGroupCallback,
    Function(bool)? iosToggleAudioSessionCallback,
    Function()? iosUpdateDevicePushTokenVoipCallback,
  }) async {
    if (!isMobile) return;

    FlutterCallkitIncoming.onEvent.listen((CallEvent? event) {
      if (event == null) return;
      log.t('FlutterCallkitIncoming event:${event.toString()}');
      switch (event.event) {
        case Event.actionCallIncoming:
          // received an incoming call
          setCallkitRinging();
          Map extra = event.body['extra'] as Map;
          if (extra.containsKey('iosUuid') && extra['iosUuid'] != null) {
            // set uuid for ios incoming call from Native Swift
            _uuid = event.body['extra']['iosUuid'];
            _callDirection = CallDirection.incoming;
          } else if (uuid == null) {
            // set uuid for android incoming call from fcm background isolate
            setActiveCallUuid();
          }
          if (incomingCallCallback != null) incomingCallCallback();
          break;
        case Event.actionCallStart:
          // started an outgoing call
          if (outgoingCallCallback != null) outgoingCallCallback();
          break;
        case Event.actionCallAccept:
          // accepted an incoming call
          clearCallkitRinging();
          if (acceptedCallCallback != null && !acceptedCall) acceptedCallCallback(_callDirection);
          acceptedCall = true;
          break;
        case Event.actionCallDecline:
          // declined an incoming call
          if (declinedCallCallback != null) declinedCallCallback(_callDirection);
          _resetCallState();
          break;
        case Event.actionCallEnded:
          // ended an incoming/outgoing call
          if (endedCallCallback != null) endedCallCallback();
          _resetCallState();
          break;
        case Event.actionCallTimeout:
          // missed an incoming call
          if (missedCallCallback != null) missedCallCallback();
          _resetCallState();
          break;
        case Event.actionCallCallback:
          // only Android - click action `Call back` from missed call notification
          if (callBackCallback != null) callBackCallback();
          break;
        case Event.actionCallToggleHold:
          // only iOS
          if (iosToggleHoldCallCallback != null) iosToggleHoldCallCallback();
          break;
        case Event.actionCallToggleMute:
          // only iOS
          bool isMuted = event.body['isMuted'] as bool;
          if (iosToggleMuteCallCallback != null) iosToggleMuteCallCallback(isMuted);
          break;
        case Event.actionCallToggleDmtf:
          // only iOS
          String tone = event.body['digits'] as String;
          if (iosToggleDmtfCallback != null) iosToggleDmtfCallback(tone);
          break;
        case Event.actionCallToggleGroup:
          // only iOS
          if (iosToggleGroupCallback != null) iosToggleGroupCallback();
          break;
        case Event.actionCallToggleAudioSession:
          // only iOS
          bool isActivate = event.body['isActivate'] as bool;
          if (iosToggleAudioSessionCallback != null) iosToggleAudioSessionCallback(isActivate);
          break;
        case Event.actionCallConnected:
          break;
        case Event.actionDidUpdateDevicePushTokenVoip:
          // only iOS
          if (iosUpdateDevicePushTokenVoipCallback != null) iosUpdateDevicePushTokenVoipCallback();
          break;
        case Event.actionCallCustom:
          // for custom action
          break;
      }
    });

    log.t('callkitService init completed');
  }

  /// Android and iOS
  Future<void> incomingCall(String callerName, String callerId) async {
    if (!isMobile) return;

    // We don't support multiple calls at once
    bool hasActiveCall = await hasActiveCalls();
    if (hasActiveCall) {
      log.w('callkitService incomingCall - skipping due to active call');
      return;
    }

    _uuid = const Uuid().v4();
    CallKitParams params = CallKitParams(
      id: _uuid,
      nameCaller: callerName,
      appName: 'DDOne',
      handle: callerId,
      type: 0,
      duration: callkitExpire,
      textAccept: 'Accept',
      textDecline: 'Decline',
      missedCallNotification: const NotificationParams(
        showNotification: false,
        isShowCallback: false,
        subtitle: 'Missed call',
        callbackText: 'Call back',
      ),
      extra: <String, dynamic>{
        'caller': callerName,
        'callerId': callerId,
      },
      // headers: <String, dynamic> {},
      android: const AndroidParams(
        isCustomNotification: false,
        isCustomSmallExNotification: false,
        isShowLogo: false,
        isShowCallID: true,
        ringtonePath: 'incoming_call2',
        backgroundColor: '#222222',
        actionColor: '#4CAF50',
        textColor: '#ffffff',
        incomingCallNotificationChannelName: 'Incoming Call',
        missedCallNotificationChannelName: 'Missed Call',
        isShowFullLockedScreen: true,
        isImportant: false,
        isBot: false,
      ),
      ios: const IOSParams(
        iconName: 'CallKitLogo',
        handleType: 'generic',
        supportsVideo: false,
        maximumCallGroups: 1,
        maximumCallsPerCallGroup: 1,
        audioSessionMode: 'voiceChat',
        audioSessionActive: true,
        audioSessionPreferredSampleRate: 44100.0,
        audioSessionPreferredIOBufferDuration: 0.005,
        supportsDTMF: true,
        supportsHolding: true,
        supportsGrouping: false,
        supportsUngrouping: false,
        ringtonePath: 'system_ringtone_default',
      ),
    );
    setCallkitRinging();
    _callDirection = CallDirection.incoming;
    await FlutterCallkitIncoming.showCallkitIncoming(params);
  }

  /// Android and iOS
  Future<void> showMissCall(String callerName, String callerId) async {
    if (!isMobile) return;
    if (uuid == null) return;
    CallKitParams params = CallKitParams(
      id: _uuid,
      nameCaller: callerName,
      handle: callerId,
      type: 0,
      missedCallNotification: const NotificationParams(
        showNotification: true,
        isShowCallback: false,
        subtitle: 'Missed call',
        callbackText: 'Call back',
      ),
      android: const AndroidParams(
        isCustomNotification: false,
        isShowCallID: true,
      ),
      extra: <String, dynamic>{
        'caller': callerName,
        'callerId': callerId,
      },
    );
    await FlutterCallkitIncoming.showMissCallNotification(params);
  }

  /// Android
  Future<void> hideNotificationCall() async {
    if (!isMobile) return;
    if (_uuid == null) return;
    CallKitParams params = CallKitParams(
      id: _uuid,
    );
    await FlutterCallkitIncoming.hideCallkitIncoming(params);
  }

  /// Android and iOS
  Future<void> outgoingCall(String callerName, String callerId) async {
    if (!isMobile) return;

    _uuid = const Uuid().v4();

    CallKitParams params = CallKitParams(
        id: _uuid,
        nameCaller: callerName,
        handle: callerId,
        type: 0,
        extra: <String, dynamic>{
          'caller': callerName,
          'callerId': callerId,
        },
        ios: const IOSParams(handleType: 'generic'),
        android: const AndroidParams(
          isCustomNotification: false,
          isShowCallID: true,
        ));
    _callDirection = CallDirection.outgoing;
    await FlutterCallkitIncoming.startCall(params);
  }

  /// Android and iOS
  Future<void> endCall() async {
    if (!isMobile) return;
    if (_uuid == null) return;
    await FlutterCallkitIncoming.endCall(_uuid!);
    // Reset state after ending call
    _resetCallState();
  }

  /// Android and iOS
  Future<void> endAllCall() async {
    if (!isMobile) return;
    await FlutterCallkitIncoming.endAllCalls();
    // Reset state after ending all calls
    _resetCallState();
  }

  /// Reset call state to allow new calls
  void _resetCallState() {
    clearCallkitRinging();
    _uuid = null;
    _callDirection = null;
    acceptedCall = false;
  }

  /// Android and iOS
  Future<dynamic> getActiveCalls() async {
    if (!isMobile) return null;
    dynamic activeCalls = await FlutterCallkitIncoming.activeCalls();
    log.t('callkitService - getActiveCalls:$activeCalls');
    return activeCalls;
  }

  /// iOS
  /// - this is required after call connection is establised
  Future<void> connectedCall() async {
    if (!isIOS) return;
    if (_uuid == null) return;
    log.d('callkitSevice - connectedCall');
    await FlutterCallkitIncoming.setCallConnected(_uuid!);
  }

  /// iOS
  Future<String?> getDevicePushTokenVoIP() async {
    if (!isIOS) return null;
    dynamic iosVoipToken = await FlutterCallkitIncoming.getDevicePushTokenVoIP();
    return iosVoipToken as String;
  }

  Future<bool> hasActiveCalls() async {
    dynamic activeCalls = await getActiveCalls();
    if (activeCalls is List && activeCalls.isNotEmpty) return true;
    return false;
  }

  Future<void> setActiveCallUuid() async {
    dynamic activeCalls = await getActiveCalls();
    if (activeCalls is List && activeCalls.isNotEmpty) {
      _uuid ??= (activeCalls[0] as Map)['id'] as String;
    }
  }

  Future<Map?> getCallExtraInfo() async {
    dynamic activeCalls = await getActiveCalls();
    if (activeCalls is List && activeCalls.isNotEmpty) {
      return (activeCalls[0] as Map)['extra'] as Map;
    }
    return null;
  }

  /// Retrieves information about the current caller.
  ///
  /// This function asynchronously fetches call-related details using `getCallExtraInfo()`.
  /// It then extracts the `callerId` and `caller` name from the retrieved information.
  /// If `getCallExtraInfo()` returns `null`, default empty strings are used for
  /// both `callerId` and `caller`.
  ///
  /// Returns a `Future<Map<String, String>>` containing:
  /// - `'callerId'`: The ID of the caller (String).
  /// - `'caller'`: The name of the caller (String).
  ///
  /// Example:
  /// ```dart
  /// Map<String, String> info = await getCallerInfo();
  /// print('Caller ID: ${info['callerId']}');
  /// print('Caller Name: ${info['caller']}');
  /// ```
  Future<Map<String, String>> getCallerInfo() async {
    Map? callInfo = await getCallExtraInfo();
    String callerId = '';
    String caller = '';
    if (callInfo != null) {
      callerId = callInfo['callerId'] as String? ?? '';
      caller = callInfo['caller'] as String? ?? '';
    }
    return {
      'callerId': callerId,
      'caller': caller,
    };
  }

  Future<void> setCallkitRinging() async {
    await prefs.setBool(CacheKeys.callkitRinging, true, ttlInSeconds: (callkitExpire / 1000).toInt());
  }

  Future<void> clearCallkitRinging() async {
    await prefs.remove(CacheKeys.callkitRinging);
  }
}

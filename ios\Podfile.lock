PODS:
  - app_tracking_transparency (0.0.1):
    - Flutter
  - audio_session (0.0.1):
    - Flutter
  - background_downloader (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - CryptoSwift (1.8.4)
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - file_selector_ios (0.0.1):
    - Flutter
  - Firebase/CoreOnly (12.0.0):
    - FirebaseCore (~> 12.0.0)
  - Firebase/Messaging (12.0.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 12.0.0)
  - firebase_core (4.0.0):
    - Firebase/CoreOnly (= 12.0.0)
    - Flutter
  - firebase_messaging (16.0.0):
    - Firebase/Messaging (= 12.0.0)
    - firebase_core
    - Flutter
  - FirebaseCore (12.0.0):
    - FirebaseCoreInternal (~> 12.0.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreInternal (12.0.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseInstallations (12.0.0):
    - FirebaseCore (~> 12.0.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (12.0.0):
    - FirebaseCore (~> 12.0.0)
    - FirebaseInstallations (~> 12.0.0)
    - GoogleDataTransport (~> 10.1)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_background_service_ios (0.0.3):
    - Flutter
  - flutter_callkit_incoming (0.0.1):
    - CryptoSwift
    - Flutter
  - flutter_contacts (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - flutter_volume_controller (0.0.1):
    - Flutter
  - flutter_webrtc (0.14.0):
    - Flutter
    - WebRTC-SDK (= 125.6422.07)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - heif_converter (1.0.0):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - media_kit_libs_ios_video (1.0.4):
    - Flutter
  - media_kit_video (0.0.1):
    - Flutter
  - MTBBarcodeScanner (5.0.11)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - network_info_plus (0.0.1):
    - Flutter
  - open_filex (0.0.2):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - phone_state (2.1.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - url_launcher_ios (0.0.1):
    - Flutter
  - volume_controller (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - WebRTC-SDK (125.6422.07)

DEPENDENCIES:
  - app_tracking_transparency (from `.symlinks/plugins/app_tracking_transparency/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - background_downloader (from `.symlinks/plugins/background_downloader/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - file_selector_ios (from `.symlinks/plugins/file_selector_ios/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_background_service_ios (from `.symlinks/plugins/flutter_background_service_ios/ios`)
  - flutter_callkit_incoming (from `.symlinks/plugins/flutter_callkit_incoming/ios`)
  - flutter_contacts (from `.symlinks/plugins/flutter_contacts/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_volume_controller (from `.symlinks/plugins/flutter_volume_controller/ios`)
  - flutter_webrtc (from `.symlinks/plugins/flutter_webrtc/ios`)
  - heif_converter (from `.symlinks/plugins/heif_converter/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - media_kit_libs_ios_video (from `.symlinks/plugins/media_kit_libs_ios_video/ios`)
  - media_kit_video (from `.symlinks/plugins/media_kit_video/ios`)
  - network_info_plus (from `.symlinks/plugins/network_info_plus/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - phone_state (from `.symlinks/plugins/phone_state/ios`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - volume_controller (from `.symlinks/plugins/volume_controller/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - CryptoSwift
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - MTBBarcodeScanner
    - nanopb
    - PromisesObjC
    - SDWebImage
    - SwiftyGif
    - WebRTC-SDK

EXTERNAL SOURCES:
  app_tracking_transparency:
    :path: ".symlinks/plugins/app_tracking_transparency/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  background_downloader:
    :path: ".symlinks/plugins/background_downloader/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  file_selector_ios:
    :path: ".symlinks/plugins/file_selector_ios/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_background_service_ios:
    :path: ".symlinks/plugins/flutter_background_service_ios/ios"
  flutter_callkit_incoming:
    :path: ".symlinks/plugins/flutter_callkit_incoming/ios"
  flutter_contacts:
    :path: ".symlinks/plugins/flutter_contacts/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_volume_controller:
    :path: ".symlinks/plugins/flutter_volume_controller/ios"
  flutter_webrtc:
    :path: ".symlinks/plugins/flutter_webrtc/ios"
  heif_converter:
    :path: ".symlinks/plugins/heif_converter/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  media_kit_libs_ios_video:
    :path: ".symlinks/plugins/media_kit_libs_ios_video/ios"
  media_kit_video:
    :path: ".symlinks/plugins/media_kit_video/ios"
  network_info_plus:
    :path: ".symlinks/plugins/network_info_plus/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  phone_state:
    :path: ".symlinks/plugins/phone_state/ios"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  volume_controller:
    :path: ".symlinks/plugins/volume_controller/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

SPEC CHECKSUMS:
  app_tracking_transparency: ****************************************
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  background_downloader: 50e91d979067b82081aba359d7d916b3ba5fadad
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  CryptoSwift: e64e11850ede528a02a0f3e768cec8e9d92ecb90
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: a0560bc09d61de87f12d246fc47d2119e6ef37be
  file_selector_ios: f92e583d43608aebc2e4a18daac30b8902845502
  Firebase: 800d487043c0557d9faed71477a38d9aafb08a41
  firebase_core: 633e1851ffe1b9ab875f6467a4f574c79cef02e4
  firebase_messaging: d17feef781edc84ebefe62624fb384358ad96361
  FirebaseCore: 055f4ab117d5964158c833f3d5e7ec6d91648d4a
  FirebaseCoreInternal: dedc28e569a4be85f38f3d6af1070a2e12018d55
  FirebaseInstallations: d4c7c958f99c8860d7fcece786314ae790e2f988
  FirebaseMessaging: af49f8d7c0a3d2a017d9302c80946f45a7777dde
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_background_service_ios: 00d31bdff7b4bfe06d32375df358abe0329cf87e
  flutter_callkit_incoming: cb8138af67cda6dd981f7101a5d709003af21502
  flutter_contacts: 5383945387e7ca37cf963d4be57c21f2fc15ca9f
  flutter_local_notifications: a5a732f069baa862e728d839dd2ebb904737effb
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  flutter_volume_controller: c2be490cb0487e8b88d0d9fc2b7e1c139a4ebccb
  flutter_webrtc: fd0d3bdef8766a0736dbbe2e5b7e85f1f3c52117
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  heif_converter: c99800312529d736276a6a75a2115b8f43a0bad1
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  media_kit_libs_ios_video: 5a18affdb97d1f5d466dc79988b13eff6c5e2854
  media_kit_video: 1746e198cb697d1ffb734b1d05ec429d1fcd1474
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  network_info_plus: cf61925ab5205dce05a4f0895989afdb6aade5fc
  open_filex: 432f3cd11432da3e39f47fcc0df2b1603854eff1
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  phone_state: 498a43ae8795d4341a78a12ac2144cd0ac7a77cd
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  qr_code_scanner: d77f94ecc9abf96d9b9b8fc04ef13f611e5a147a
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  volume_controller: 3657a1f65bedb98fa41ff7dc5793537919f31b12
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  WebRTC-SDK: dff00a3892bc570b6014e046297782084071657e

PODFILE CHECKSUM: bac9c2368188b3b2be2b9a93bf1a09b81442d514

COCOAPODS: 1.16.2

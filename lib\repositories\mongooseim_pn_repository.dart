import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'mongooseim_pn_repository.g.dart';

@RestApi()
abstract class MongooseimPnRepository {
  factory MongooseimPnRepository(Dio dio, {String baseUrl}) = _MongooseimPnRepository;

  @POST('updateDeviceStatus')
  Future<HttpResponse<void>> informAppOpen(@Body() dynamic params);

  @DELETE('updateDeviceStatus')
  Future<HttpResponse<void>> informAppClose(@Body() dynamic params);
}

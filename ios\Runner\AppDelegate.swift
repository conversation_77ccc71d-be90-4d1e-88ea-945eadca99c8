import CallKit
import Flutter
import PushKit
import UIKit
import flutter_callkit_incoming
import flutter_local_notifications

@UIApplicationMain
// @objc class AppDelegate: FlutterAppDelegate {
@objc class AppDelegate: FlutterAppDelegate, PKPushRegistryDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    FlutterLocalNotificationsPlugin.setPluginRegistrantCallback { (registry) in  // setup local_notification
      GeneratedPluginRegistrant.register(with: registry)
    }
    if #available(iOS 10.0, *) {  // setup local_notification
      UNUserNotificationCenter.current().delegate = self as UNUserNotificationCenterDelegate
    }
    GeneratedPluginRegistrant.register(with: self)
    registerForVoIPPushNotifications()  // setup VoIP
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  func registerForVoIPPushNotifications() {
    let mainQueue = DispatchQueue.main
    let voipRegistry: PKPushRegistry = PKPushRegistry(queue: mainQueue)
    voipRegistry.delegate = self
    voipRegistry.desiredPushTypes = [PKPushType.voIP]
  }

  // Handle updated push credentials
  @objc func pushRegistry(
    _ registry: PKPushRegistry, didUpdate credentials: PKPushCredentials, for type: PKPushType
  ) {
    let deviceToken = credentials.token.map { String(format: "%02x", $0) }.joined()
    SwiftFlutterCallkitIncomingPlugin.sharedInstance?.setDevicePushTokenVoIP(deviceToken)
    print("Swift - VoIP token received: \(deviceToken)")
  }

  @objc func pushRegistry(_ registry: PKPushRegistry, didInvalidatePushTokenFor type: PKPushType) {
    SwiftFlutterCallkitIncomingPlugin.sharedInstance?.setDevicePushTokenVoIP("")
    print("Swift - VoIP token invalidated for type \(type)")
  }

  // Handle incoming pushes
  @objc func pushRegistry(
    _ registry: PKPushRegistry, didReceiveIncomingPushWith payload: PKPushPayload,
    for type: PKPushType, completion: @escaping () -> Void
  ) {
    guard type == .voIP else { return }

    print("Swift - Received VoIP push with payload \(payload.dictionaryPayload)")
    let uuid = UUID().uuidString
    var caller = ""
    var callerId = ""
    var category = ""
    if let apsDictionary = payload.dictionaryPayload["aps"] as? [String: Any] {
      caller = apsDictionary["caller"] as? String ?? ""
      callerId = apsDictionary["callerId"] as? String ?? ""
      category = apsDictionary["category"] as? String ?? ""
    }

    let calls = SwiftFlutterCallkitIncomingPlugin.sharedInstance?.activeCalls()

    let data = flutter_callkit_incoming.Data(
      id: uuid, nameCaller: caller, handle: callerId, type: 0)
    data.appName = "DDOne"
    data.duration = 60000
    data.extra = ["caller": caller, "callerId": callerId, "iosUuid": uuid]
    data.handleType = "generic"
    data.maximumCallGroups = 1
    data.maximumCallsPerCallGroup = 1
    data.supportsVideo = false
    data.supportsDTMF = true
    data.supportsHolding = true
    data.supportsGrouping = false
    data.supportsUngrouping = false
    data.includesCallsInRecents = false
    data.ringtonePath = "system_ringtone_default"
    data.audioSessionMode = "voiceChat"
    data.audioSessionActive = true
    data.audioSessionPreferredSampleRate = 44100.0
    data.audioSessionPreferredIOBufferDuration = 0.005

    if category == "miss-call" && calls != nil && !calls!.isEmpty {  // end call when there is active call
      SwiftFlutterCallkitIncomingPlugin.sharedInstance?.showCallkitIncoming(data, fromPushKit: true)  // phantom call
      DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
        SwiftFlutterCallkitIncomingPlugin.sharedInstance?.endAllCalls()
      }
    } else if category == "end-call" && calls != nil && !calls!.isEmpty {  // end call when there is active call
      SwiftFlutterCallkitIncomingPlugin.sharedInstance?.showCallkitIncoming(data, fromPushKit: true)  // phantom call
      DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
        SwiftFlutterCallkitIncomingPlugin.sharedInstance?.endAllCalls()
      }
    } else if category.isEmpty && (callerId.isEmpty || calls?.isEmpty ?? true) {  // don't show callkitIncoming when we can't get callerid and when there is active call
      SwiftFlutterCallkitIncomingPlugin.sharedInstance?.showCallkitIncoming(data, fromPushKit: true)
    }

    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
      completion()
    }
  }
}

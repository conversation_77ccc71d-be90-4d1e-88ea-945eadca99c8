import 'dart:convert';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/utils/logger_util.dart';

/// Service to manage audio codec preferences
class CodecSettingsService with PrefsAware {
  static const List<String> _defaultCodecs = [
    'opus',
    'PCMA',
    'PCMU',
    'G722',
    'telephone-event'
  ];

  static const List<String> _availableCodecs = [
    'opus',
    'PCMA',
    'PCMU',
    'G722',
    'G729',
    'GSM',
    'telephone-event'
  ];

  /// Get the list of available codecs that can be configured
  List<String> get availableCodecs => List.from(_availableCodecs);

  /// Get the current user-configured codec list, or default if not set
  List<String> getConfiguredCodecs() {
    try {
      final codecsJson = prefs.getString(CacheKeys.audioCodecs);
      if (codecsJson != null) {
        final List<dynamic> codecsList = jsonDecode(codecsJson);
        return codecsList.cast<String>();
      }
    } catch (e) {
      log.e('Failed to load configured codecs', error: e);
    }
    return List.from(_defaultCodecs);
  }

  /// Save the user-configured codec list
  Future<bool> saveConfiguredCodecs(List<String> codecs) async {
    try {
      final codecsJson = jsonEncode(codecs);
      final success = await prefs.setString(CacheKeys.audioCodecs, codecsJson);
      if (success) {
        log.i('Saved configured codecs: $codecs');
      }
      return success;
    } catch (e) {
      log.e('Failed to save configured codecs', error: e);
      return false;
    }
  }

  /// Get codecs that are available but not currently configured
  List<String> getAvailableCodecs(List<String> configuredCodecs) {
    return _availableCodecs
        .where((codec) => !configuredCodecs.contains(codec))
        .toList();
  }

  /// Reset codecs to default configuration
  Future<bool> resetToDefaults() async {
    return await saveConfiguredCodecs(List.from(_defaultCodecs));
  }

  /// Validate that the codec list contains at least one codec
  bool validateCodecList(List<String> codecs) {
    return codecs.isNotEmpty && codecs.any((codec) => codec != 'telephone-event');
  }
}

class CacheKeys {
  // build related
  static const String buildFlavor = 'buildFlavor';

  // app related
  static const String endpointmgtUuid = 'endpointmgtUuid';
  static const String appOpen = 'appOpen';
  static const String fullIntentPermissionRequested = 'fullIntentPermissionRequested';

  // domain related
  static const String sipProxy = 'sipProxy';
  static const String sipDomain = 'sipDomain';
  static const String sipNumber = 'sipNumber';
  static const String sipName = 'sipName';
  static const String sipPwd = 'sipPwd';
  static const String sipContactUrlBase = 'sipContactUrlBase';
  static const String sipContactUrlParams = 'sipContactUrlParams';
  static const String sipWsUrl = 'sipWsUrl';
  static const String pnUrl = 'pnUrl';
  static const String sipHeaderToken = 'sipHeaderToken';
  static const String isMessageHistory = 'isMessageHistory';

  // theme related
  static const String themeKey = 'theme_key';
  static const String languageKey = 'language_key';

  // call related
  static const String callkitRinging = 'callkit_ringing';
  static const String callkitStartCallTime = 'callkit_start_call_time';
  static const String missCall = 'miss_call';
  static const String missCallName = 'miss_call_name';
  static const String missCallId = 'miss_call_id';
  static const String missCallTime = 'miss_call_time';
  static const String lastDialed = 'last_dialed';

  // foreground service related
  static const String acceptedCallFromBackground = 'acceptedCallFromBackground';
  static const String mainAppDisposeComplete = 'mainAppDisposeComplete';

  // firebase related
  static const String firebaseMessageId = 'firebaseMessageId';

  // chat related
  static const String jidResourceId = 'jidResourceId';

  // codec settings related
  static const String audioCodecs = 'audioCodecs';
}
